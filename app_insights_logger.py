import time
import uuid
import functools
import requests
from applicationinsights import TelemetryClient

class AppInsightsLogger:
    def __init__(self, connection_string: str, cloud_role: str = "python-app"):
        """Initialize Application Insights logger"""
        self.client = TelemetryClient(connection_string)
        self.cloud_role = cloud_role

    # -----------------------------
    # Core Logging
    # -----------------------------
    def log_request(self, name: str, url: str, duration: float, success: bool, response_code: str):
        self.client.track_request(
            name=name,
            url=url,
            success=success,
            duration=duration,
            response_code=response_code,
            properties={"cloud_role": self.cloud_role}
        )
        self.client.flush()

    def log_exception(self, ex: Exception, properties: dict = None):
        import sys
        exc_type, exc_value, exc_traceback = sys.exc_info()
        self.client.track_exception(
            type=exc_type,
            value=exc_value,
            tb=exc_traceback,
            properties=properties
        )
        self.client.flush()

    def log_event(self, name: str, properties: dict = None):
        self.client.track_event(name, properties)
        self.client.flush()

    def log_metric(self, name: str, value: float, properties: dict = None):
        self.client.track_metric(name, value, properties)
        self.client.flush()

    # -----------------------------
    # User Behavior Analysis
    # -----------------------------
    def log_user_event(self, user_id: str, session_id: str, event_name: str, properties: dict = None):
        props = {"user_id": user_id, "session_id": session_id}
        if properties:
            props.update(properties)
        self.log_event(event_name, props)

    # -----------------------------
    # Availability Testing
    # -----------------------------
    def test_availability(self, endpoint: str, test_name: str = None):
        start = time.time()
        try:
            response = requests.get(endpoint, timeout=5)
            duration = time.time() - start
            self.log_request(
                name=test_name or f"Availability-{endpoint}",
                url=endpoint,
                duration=duration,
                success=response.status_code == 200,
                response_code=str(response.status_code)
            )
            return response.status_code
        except Exception as e:
            duration = time.time() - start
            self.log_request(
                name=test_name or f"Availability-{endpoint}",
                url=endpoint,
                duration=duration,
                success=False,
                response_code="500"
            )
            self.log_exception(e, {"availability_test": endpoint})
            return None

    # -----------------------------
    # Decorators
    # -----------------------------
    def track_request(self, name: str = None, url: str = None):
        """Decorator for tracking function execution as a request"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration = time.time() - start
                    self.log_request(
                        name or func.__name__,
                        url or f"/{func.__name__}",
                        duration,
                        True,
                        "200"
                    )
                    return result
                except Exception as e:
                    duration = time.time() - start
                    self.log_request(
                        name or func.__name__,
                        url or f"/{func.__name__}",
                        duration,
                        False,
                        "500"
                    )
                    self.log_exception(e)
                    raise
            return wrapper
        return decorator

    def track_metric(self, metric_name: str, log_duration: bool = True, log_return_value: bool = False):
        """Decorator for logging function execution time or return values as metrics"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start = time.time()
                result = func(*args, **kwargs)
                duration = time.time() - start

                if log_duration:
                    self.log_metric(metric_name + "_duration", duration, {"function": func.__name__})

                if log_return_value and isinstance(result, (int, float)):
                    self.log_metric(metric_name + "_value", result, {"function": func.__name__})

                return result
            return wrapper
        return decorator
