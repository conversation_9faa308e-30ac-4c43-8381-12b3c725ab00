import time
from app_insights_logger import AppInsightsLogger

logger = AppInsightsLogger("InstrumentationKey=xxxx-xxxx", cloud_role="payment-service")

# -----------------------------
# Performance Monitoring
# -----------------------------
@logger.track_request(name="PaymentProcess", url="/api/payments")
@logger.track_metric("PaymentProcess", log_duration=True)
def process_payment(order_id, amount):
    time.sleep(1.2)
    if amount <= 0:
        raise ValueError("Invalid amount")
    return amount * 1.18  # add tax

# -----------------------------
# User Behavior Analysis
# -----------------------------
logger.log_user_event("user123", "session456", "ViewedCheckoutPage", {"step": "checkout"})

# -----------------------------
# Error <PERSON>ag<PERSON>tics
# -----------------------------
try:
    process_payment("order123", -100)
except Exception as e:
    logger.log_exception(e, {"order_id": "order123"})

# -----------------------------
# Availability Testing
# -----------------------------
status = logger.test_availability("https://example.com/health")
print("Availability Test Status:", status)

# -----------------------------
# DevOps CI/CD Tracking
# -----------------------------
@logger.track_metric("BuildPipeline", log_duration=True)
def run_pipeline_stage():
    time.sleep(0.7)
    return 1  # pretend it's a stage result

run_pipeline_stage()
